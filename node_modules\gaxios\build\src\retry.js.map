{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../../src/retry.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;AAIjC,wCAgFC;AAhFM,KAAK,UAAU,cAAc,CAAC,GAAgB;IACnD,IAAI,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1D,OAAO,EAAC,WAAW,EAAE,KAAK,EAAC,CAAC;IAC9B,CAAC;IACD,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IACtB,MAAM,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,IAAI,CAAC,CAAC;IAC7D,MAAM,CAAC,KAAK;QACV,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IACzE,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,IAAI;QACvD,KAAK;QACL,MAAM;QACN,KAAK;QACL,SAAS;QACT,QAAQ;KACT,CAAC;IACF,MAAM,CAAC,iBAAiB;QACtB,MAAM,CAAC,iBAAiB,KAAK,SAAS,IAAI,MAAM,CAAC,iBAAiB,KAAK,IAAI;YACzE,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC;IAC/B,MAAM,CAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB;QACvD,CAAC,CAAC,MAAM,CAAC,oBAAoB;QAC7B,CAAC,CAAC,CAAC,CAAC;IACN,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB;QACnD,CAAC,CAAC,MAAM,CAAC,kBAAkB;QAC3B,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY;QACvC,CAAC,CAAC,MAAM,CAAC,YAAY;QACrB,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAC5B,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa;QACzC,CAAC,CAAC,MAAM,CAAC,aAAa;QACtB,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAE5B,2DAA2D;IAC3D,kCAAkC;IAClC,MAAM,WAAW,GAAG;QAClB,0DAA0D;QAC1D,wDAAwD;QACxD,+BAA+B;QAC/B,gCAAgC;QAChC,qCAAqC;QACrC,kCAAkC;QAClC,oCAAoC;QACpC,8BAA8B;QAC9B,CAAC,GAAG,EAAE,GAAG,CAAC;QACV,CAAC,GAAG,EAAE,GAAG,CAAC;QACV,CAAC,GAAG,EAAE,GAAG,CAAC;QACV,CAAC,GAAG,EAAE,GAAG,CAAC;KACX,CAAC;IACF,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,IAAI,WAAW,CAAC;IAErE,mCAAmC;IACnC,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC;IAEhC,2CAA2C;IAC3C,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,IAAI,kBAAkB,CAAC;IAC/D,IAAI,CAAC,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QAChC,OAAO,EAAC,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAC,CAAC;IAClD,CAAC;IAED,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAExC,kDAAkD;IAClD,GAAG,CAAC,MAAM,CAAC,WAAY,CAAC,mBAAoB,IAAI,CAAC,CAAC;IAElD,iEAAiE;IACjE,MAAM,OAAO,GAAG,MAAM,CAAC,YAAY;QACjC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;QACjC,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACpB,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IAEP,4DAA4D;IAC5D,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC1B,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED,kEAAkE;IAClE,MAAM,OAAO,CAAC;IACd,OAAO,EAAC,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAC,CAAC;AACjD,CAAC;AAED;;;GAGG;AACH,SAAS,kBAAkB,CAAC,GAAgB;;IAC1C,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IAE9B,+CAA+C;IAC/C,6EAA6E;IAC7E,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,CAAA,MAAA,GAAG,CAAC,KAAK,0CAAE,IAAI,MAAK,YAAY,EAAE,CAAC;QAClE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,yDAAyD;IACzD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,kEAAkE;IAClE,IACE,CAAC,GAAG,CAAC,QAAQ;QACb,CAAC,MAAM,CAAC,mBAAmB,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,iBAAkB,EAC9D,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,0CAA0C;IAC1C,IACE,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM;QAClB,MAAM,CAAC,kBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,EACvE,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,2DAA2D;IAC3D,kCAAkC;IAClC,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QACxC,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,kBAAmB,EAAE,CAAC;YACpD,MAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YACnC,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;gBACnC,SAAS,GAAG,IAAI,CAAC;gBACjB,MAAM;YACR,CAAC;QACH,CAAC;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,0CAA0C;IAC1C,MAAM,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,IAAI,CAAC,CAAC;IAC7D,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,KAAM,EAAE,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,SAAS,SAAS,CAAC,GAAgB;IACjC,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAChD,OAAO,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC;IAChC,CAAC;IACD,OAAO;AACT,CAAC;AAED;;;;;GAKG;AACH,SAAS,iBAAiB,CAAC,MAAmB;;IAC5C,mDAAmD;IACnD,gEAAgE;IAChE,MAAM,UAAU,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAA,MAAM,CAAC,UAAU,mCAAI,GAAG,CAAC;IAC7E,oFAAoF;IACpF,MAAM,eAAe,GACnB,UAAU;QACV,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,oBAAqB,EAAE,MAAM,CAAC,mBAAoB,CAAC,GAAG,CAAC,CAAC;YACxE,CAAC,CAAC;YACF,IAAI,CAAC;IACT,MAAM,iBAAiB,GACrB,MAAM,CAAC,YAAa,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,kBAAmB,CAAC,CAAC;IAEnE,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,iBAAiB,EAAE,MAAM,CAAC,aAAc,CAAC,CAAC;AAC7E,CAAC"}